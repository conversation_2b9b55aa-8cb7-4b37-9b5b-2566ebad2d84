import { useEffect, useMemo, useState } from "react";
import type { Transaction, SummarizeRequest, SummarizeResponse } from "@shared/api";
import { computePercentages, groupByCategory, suggestBudget } from "@/lib/finance";

export default function InsightsPanel({ transactions }: { transactions: Transaction[] }) {
  const byCategory = useMemo(() => groupByCategory(transactions), [transactions]);
  const withPct = useMemo(() => computePercentages(byCategory), [byCategory]);
  const budget = useMemo(() => suggestBudget(byCategory), [byCategory]);
  const [summary, setSummary] = useState<string>("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!transactions.length) {
      setSummary("");
      return;
    }
    const text = `Spending summary by category: ${withPct
      .map((c) => `${c.category} ${Math.round(c.pct)}%`)
      .join(", ")}. 50-30-20 guideline target: Needs 50%, Wants 30%, Savings 20%. Current: Needs ${(budget.needsTotal / budget.total) * 100 || 0}%, Wants ${(budget.wantsTotal / budget.total) * 100 || 0}%, Savings ${(budget.savingsTotal / budget.total) * 100 || 0}%. Provide concrete tips to adjust categories.`;
    summarize(text);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [transactions]);

  async function summarize(text: string) {
    setLoading(true);
    try {
      const body: SummarizeRequest = { text };
      const r = await fetch("/api/summarize", { method: "POST", headers: { "content-type": "application/json" }, body: JSON.stringify(body) });
      const data = (await r.json()) as SummarizeResponse & { error?: string };
      setSummary(data.summary || data.error || "");
    } catch (e: any) {
      setSummary(e?.message || "");
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="rounded-2xl border bg-card shadow-sm">
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold">Personalized Insights</h2>
        <p className="text-sm text-muted-foreground">Summaries and tips based on your spending</p>
      </div>
      <div className="p-4 space-y-4">
        {!transactions.length && <p className="text-sm text-muted-foreground">Upload data to see insights.</p>}
        {transactions.length > 0 && (
          <>
            <div className="grid grid-cols-3 gap-3">
              <StatCard label="Total" value={`$${budget.total.toFixed(2)}`} />
              <StatCard label="Needs" value={`${((budget.needsTotal / budget.total) * 100 || 0).toFixed(0)}%`} delta={budget.needsDelta} />
              <StatCard label="Wants" value={`${((budget.wantsTotal / budget.total) * 100 || 0).toFixed(0)}%`} delta={budget.wantsDelta} />
            </div>
            <div className="rounded-xl border bg-background p-4">
              <p className="text-sm text-muted-foreground mb-2">AI Summary</p>
              <p className="whitespace-pre-wrap">{loading ? "Summarizing..." : summary}</p>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

function StatCard({ label, value, delta }: { label: string; value: string; delta?: number }) {
  return (
    <div className="rounded-xl border bg-background p-4">
      <p className="text-sm text-muted-foreground">{label}</p>
      <p className="text-2xl font-semibold">{value}</p>
      {typeof delta === "number" && (
        <p className={delta > 0 ? "text-amber-600" : delta < 0 ? "text-emerald-600" : "text-muted-foreground"}>
          {delta > 0 ? `+${delta}% vs 50-30-20` : `${delta}% vs 50-30-20`}
        </p>
      )}
    </div>
  );
}
