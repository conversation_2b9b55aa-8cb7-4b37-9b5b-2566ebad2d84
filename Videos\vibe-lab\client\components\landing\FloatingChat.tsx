import ChatPanel from "@/components/finance/ChatPanel";
import { useState } from "react";

export default function FloatingChat() {
  const [open, setOpen] = useState(true);
  return (
    <div className="fixed bottom-6 right-6 z-40">
      <div className="mb-2 flex justify-end">
        <button onClick={() => setOpen((v) => !v)} className="rounded-full bg-primary px-4 py-2 text-primary-foreground shadow-lg">
          {open ? "Hide Chat" : "Chat"}
        </button>
      </div>
      {open && (
        <div className="h-[520px] w-[360px] overflow-hidden rounded-2xl border bg-white shadow-2xl">
          <ChatPanel />
        </div>
      )}
    </div>
  );
}
