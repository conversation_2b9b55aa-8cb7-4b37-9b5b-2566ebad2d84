import { useAuth } from "@/context/auth";
import { useAuthUI } from "@/context/auth-ui";

export default function Header() {
  return (
    <header className="sticky top-0 z-10 border-b bg-white/70 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="container flex h-16 items-center justify-between">
        <a href="#home" className="flex items-center gap-2 font-bold">
          <span className="inline-flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-primary to-accent text-white shadow-sm">
            <svg viewBox="0 0 24 24" className="h-5 w-5" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M4 5h10"/>
              <path d="M4 9h10"/>
              <path d="M8 5c0 3 5 3 6 5"/>
              <path d="M10 12l-3 5"/>
              <path d="M21 15v4a2 2 0 0 1-2 2h-6l-4 2v-4H7a2 2 0 0 1-2-2v-1"/>
            </svg>
          </span>
          <span className="text-xl">FinWise AI</span>
        </a>
        <nav className="hidden md:flex items-center gap-6 text-sm text-muted-foreground">
          <a href="#home" className="hover:text-foreground">Home</a>
          <a href="#dashboard" className="hover:text-foreground">Dashboard</a>
          <a href="#budget" className="hover:text-foreground">Budget Planner</a>
          <a href="#expenses" className="hover:text-foreground">Expense Tracker</a>
          <a href="#chat" className="hover:text-foreground">AI Chat</a>
          <a href="#calculators" className="hover:text-foreground">Calculators</a>
          <a href="#reports" className="hover:text-foreground">Reports & Insights</a>
          <a href="#faq" className="hover:text-foreground">FAQ / Knowledge Hub</a>
        </nav>
        <HeaderActions />
      </div>
    </header>
  );
}

function HeaderActions() {
  const { isAuthenticated, user, logout } = useAuth();
  const ui = useAuthUI();
  if (!isAuthenticated)
    return (
      <div className="flex items-center gap-3">
        <button onClick={() => ui.openAuth("register")} className="rounded-xl bg-primary px-4 py-2 text-primary-foreground">Register</button>
        <button onClick={() => ui.openAuth("login")} className="rounded-xl bg-secondary px-4 py-2">Login</button>
      </div>
    );
  return (
    <div className="flex items-center gap-3">
      <span className="text-sm text-muted-foreground">{user?.name}</span>
      <button onClick={logout} className="rounded-xl bg-secondary px-4 py-2">Logout</button>
    </div>
  );
}
