import "dotenv/config";
import express from "express";
import cors from "cors";
import { handleDemo } from "./routes/demo";
import { chatHandler, classifyExpensesHandler, summarize<PERSON>andler, embedHandler } from "./routes/huggingface";

export function createServer() {
  const app = express();

  // Middleware
  app.use(cors());
  app.use(express.json({ limit: "2mb" }));
  app.use(express.urlencoded({ extended: true }));

  // Example API routes
  app.get("/api/ping", (_req, res) => {
    const ping = process.env.PING_MESSAGE ?? "ping";
    res.json({ message: ping });
  });

  app.get("/api/demo", handleDemo);

  // Hugging Face powered routes
  app.post("/api/chat", chatHandler);
  app.post("/api/classify", classifyExpensesHandler);
  app.post("/api/summarize", summarizeHandler);
  app.post("/api/embed", embedHandler);

  return app;
}
