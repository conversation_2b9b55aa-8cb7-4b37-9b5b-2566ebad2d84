/** @import must precede all other statements */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Tailwind CSS theme
   * tailwind.config.ts expects the following color variables to be expressed as HSL values.
   * A different format will require also updating the theme in tailwind.config.ts.
  */
  :root {
    --background: 210 40% 98%;
    --foreground: 224 71% 4%;

    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    --primary: 160 84% 39%;
    --primary-foreground: 144 61% 95%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 224 71% 10%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 20% 50%;

    --accent: 191 97% 37%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 72% 50%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 160 84% 39%;

    --radius: 0.75rem;

    --sidebar-background: 210 40% 98%;
    --sidebar-foreground: 224 71% 4%;
    --sidebar-primary: 160 84% 39%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 224 71% 10%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 160 84% 39%;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 210 40% 98%;

    --card: 224 71% 6%;
    --card-foreground: 210 40% 98%;

    --popover: 224 71% 6%;
    --popover-foreground: 210 40% 98%;

    --primary: 160 84% 39%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 14% 18%;
    --secondary-foreground: 210 40% 98%;

    --muted: 220 14% 18%;
    --muted-foreground: 217 15% 65%;

    --accent: 191 97% 37%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 14% 18%;
    --input: 220 14% 18%;
    --ring: 160 84% 39%;
    --sidebar-background: 224 71% 6%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 160 84% 39%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 14% 18%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 220 14% 18%;
    --sidebar-ring: 160 84% 39%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
