import type { Transaction } from "@shared/api";

export function parseCsv(content: string): Transaction[] {
  const lines = content.split(/\r?\n/).filter((l) => l.trim().length > 0);
  if (lines.length === 0) return [];
  const headerLine = lines[0];
  const headers = splitCsvLine(headerLine).map((h) => h.trim().toLowerCase());
  const idx = {
    date: headers.findIndex((h) => h === "date"),
    description: headers.findIndex((h) => h === "description" || h === "details" || h === "name"),
    amount: headers.findIndex((h) => h === "amount" || h === "value"),
    category: headers.findIndex((h) => h === "category"),
  };
  const rows = lines.slice(1);
  const txs: Transaction[] = [];
  for (const row of rows) {
    const cols = splitCsvLine(row);
    const description = idx.description >= 0 ? cols[idx.description] : cols[0];
    const amountRaw = idx.amount >= 0 ? cols[idx.amount] : cols[1];
    const date = idx.date >= 0 ? cols[idx.date] : undefined;
    const category = idx.category >= 0 ? cols[idx.category] : undefined;
    const amount = parseFloat((amountRaw || "").replace(/[^0-9.-]/g, ""));
    if (!description || isNaN(amount)) continue;
    txs.push({ description: description.trim(), amount, date, category: category?.trim() });
  }
  return txs;
}

function splitCsvLine(line: string): string[] {
  const result: string[] = [];
  let current = "";
  let inQuotes = false;
  for (let i = 0; i < line.length; i++) {
    const ch = line[i];
    if (ch === '"') {
      if (inQuotes && line[i + 1] === '"') {
        current += '"';
        i++; // skip escaped quote
      } else {
        inQuotes = !inQuotes;
      }
    } else if (ch === "," && !inQuotes) {
      result.push(current);
      current = "";
    } else {
      current += ch;
    }
  }
  result.push(current);
  return result.map((c) => c.trim());
}

export function groupByCategory(transactions: Transaction[]): { category: string; total: number }[] {
  const map = new Map<string, number>();
  for (const t of transactions) {
    const key = t.category || "Other";
    map.set(key, (map.get(key) || 0) + t.amount);
  }
  return Array.from(map.entries())
    .map(([category, total]) => ({ category, total }))
    .sort((a, b) => b.total - a.total);
}

export function groupByMonth(transactions: Transaction[]): { month: string; total: number }[] {
  const map = new Map<string, number>();
  for (const t of transactions) {
    const d = t.date ? new Date(t.date) : undefined;
    const key = d && !isNaN(d.getTime()) ? `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}` : "Unknown";
    map.set(key, (map.get(key) || 0) + t.amount);
  }
  return Array.from(map.entries())
    .map(([month, total]) => ({ month, total }))
    .sort((a, b) => a.month.localeCompare(b.month));
}

export function computePercentages(byCategory: { category: string; total: number }[]) {
  const total = byCategory.reduce((s, c) => s + c.total, 0) || 1;
  return byCategory.map((c) => ({ ...c, pct: (c.total / total) * 100 }));
}

export function suggestBudget(byCategory: { category: string; total: number }[]) {
  const total = byCategory.reduce((s, c) => s + c.total, 0);
  const needs = ["Rent", "Bills", "Groceries", "Transport", "Healthcare"];
  const wants = ["Entertainment", "Shopping", "Travel", "Dining", "Food"];
  let needsTotal = 0;
  let wantsTotal = 0;
  let savingsTotal = 0;
  for (const c of byCategory) {
    if (needs.includes(c.category)) needsTotal += c.total;
    else if (wants.includes(c.category)) wantsTotal += c.total;
    else savingsTotal += c.total;
  }
  const rule = { needs: 50, wants: 30, savings: 20 };
  return {
    total,
    needsTotal,
    wantsTotal,
    savingsTotal,
    rule,
    needsDelta: pctDelta(needsTotal, total, rule.needs),
    wantsDelta: pctDelta(wantsTotal, total, rule.wants),
    savingsDelta: pctDelta(savingsTotal, total, rule.savings),
  };
}

function pctDelta(value: number, total: number, targetPct: number) {
  const currentPct = total > 0 ? (value / total) * 100 : 0;
  return Math.round((currentPct - targetPct) * 10) / 10;
}

export function highestRecurring(transactions: Transaction[], months: number = 3) {
  // consider last N months based on date
  const now = new Date();
  const cutoff = new Date(now.getFullYear(), now.getMonth() - months + 1, 1);
  const filtered = transactions.filter((t) => {
    if (!t.date) return false;
    const d = new Date(t.date);
    return !isNaN(d.getTime()) && d >= cutoff;
  });
  const map = new Map<string, number>();
  for (const t of filtered) {
    const key = (t.category ? `${t.category}: ` : "") + t.description.toLowerCase();
    map.set(key, (map.get(key) || 0) + t.amount);
  }
  let top: { key: string; total: number } | null = null;
  for (const [key, total] of map.entries()) {
    if (!top || total > top.total) top = { key, total };
  }
  return top;
}
