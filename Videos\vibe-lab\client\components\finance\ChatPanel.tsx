import { useEffect, useMemo, useRef, useState } from "react";
import type React from "react";
import type { ChatMessage, ChatRequest, ChatResponse } from "@shared/api";
import { cn } from "@/lib/utils";
import { useAuth } from "@/context/auth";
import { useAuthUI } from "@/context/auth-ui";

export default function ChatPanel() {
  const [messages, setMessages] = useState<ChatMessage[]>(() => {
    try {
      const raw = localStorage.getItem("chat_history");
      return raw ? (JSON.parse(raw) as ChatMessage[]) : [
        { role: "assistant", content: "Hi! I'm your personal finance advisor. Ask me anything about budgeting, saving, or spending." },
      ];
    } catch {
      return [{ role: "assistant", content: "Hi! I'm your personal finance advisor. Ask me anything about budgeting, saving, or spending." }];
    }
  });
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const listRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    localStorage.setItem("chat_history", JSON.stringify(messages));
    listRef.current?.scrollTo({ top: listRef.current.scrollHeight, behavior: "smooth" });
  }, [messages]);

  const historyPairs = useMemo(() => {
    const pairs: { user: string; assistant: string }[] = [];
    let lastUser: string | null = null;
    for (const m of messages) {
      if (m.role === "user") lastUser = m.content;
      if (m.role === "assistant" && lastUser !== null) {
        pairs.push({ user: lastUser, assistant: m.content });
        lastUser = null;
      }
    }
    return pairs;
  }, [messages]);

  async function sendMessage() {
    const text = input.trim();
    if (!text) return;
    setInput("");
    const next = [...messages, { role: "user", content: text } as ChatMessage];
    setMessages(next);
    setLoading(true);
    try {
      const body: ChatRequest = { history: historyPairs, message: text };
      const r = await fetch("/api/chat", {
        method: "POST",
        headers: { "content-type": "application/json" },
        body: JSON.stringify(body),
      });
      const data = (await r.json()) as ChatResponse & { error?: string };
      if (data?.reply) {
        setMessages((prev) => [...prev, { role: "assistant", content: data.reply }]);
      } else {
        setMessages((prev) => [...prev, { role: "assistant", content: data?.error || "Sorry, I couldn't generate a reply right now." }]);
      }
    } catch (e: any) {
      setMessages((prev) => [...prev, { role: "assistant", content: e?.message || "Network error." }]);
    } finally {
      setLoading(false);
    }
  }

  function onKeyDown(e: React.KeyboardEvent<HTMLTextAreaElement>) {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  }

  const { isAuthenticated } = useAuth();
  const ui = useAuthUI();

  return (
    <div className="flex h-full flex-col rounded-2xl border bg-card shadow-sm">
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold">Advisor Chat</h2>
        <p className="text-sm text-muted-foreground">Conversational guidance on budgeting, saving, and spending</p>
      </div>
      {!isAuthenticated ? (
        <div className="flex-1 p-6 flex items-center justify-center text-center">
          <div>
            <p className="mb-3 text-sm text-muted-foreground">Please register or login to chat with FinWise AI.</p>
            <button onClick={() => ui.openAuth("register")} className="rounded-xl bg-primary px-4 py-2 text-primary-foreground">Register</button>
            <button onClick={() => ui.openAuth("login")} className="ml-2 rounded-xl bg-secondary px-4 py-2">Login</button>
          </div>
        </div>
      ) : (
        <>
          <div ref={listRef} className="flex-1 overflow-y-auto p-4 space-y-3">
            {messages.map((m, i) => (
              <div key={i} className={cn("max-w-[85%] rounded-xl px-4 py-3", m.role === "assistant" ? "bg-secondary text-secondary-foreground" : "bg-primary text-primary-foreground ml-auto")}>{m.content}</div>
            ))}
            {loading && <div className="w-8 h-8 rounded-full border-2 border-primary border-t-transparent animate-spin" />}
          </div>
          <div className="p-4 border-t">
            <div className="flex items-end gap-2">
              <textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={onKeyDown}
                placeholder="e.g., How should I save from a salary of 50,000?"
                className="min-h-[48px] max-h-40 flex-1 resize-y rounded-xl border bg-background px-3 py-3 focus:outline-none focus:ring-2 focus:ring-ring"
              />
              <button onClick={sendMessage} disabled={loading} className="h-11 shrink-0 rounded-xl bg-primary px-4 font-medium text-primary-foreground hover:opacity-90 disabled:opacity-50">Send</button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
