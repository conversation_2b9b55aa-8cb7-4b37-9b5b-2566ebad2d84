import { useEffect, useState } from "react";
import ChatPanel from "@/components/finance/ChatPanel";
import UploadPanel from "@/components/finance/UploadPanel";
import InsightsPanel from "@/components/finance/InsightsPanel";
import ChartsPanel from "@/components/finance/ChartsPanel";
import FloatingChat from "@/components/landing/FloatingChat";
import Hero from "@/components/landing/Hero";
import type { Transaction } from "@shared/api";
import { highestRecurring } from "@/lib/finance";

export default function Index() {
  const [transactions, setTransactions] = useState<Transaction[]>(() => {
    try {
      const raw = localStorage.getItem("transactions");
      return raw ? (JSON.parse(raw) as Transaction[]) : [];
    } catch {
      return [];
    }
  });
  const [dataQuery, setDataQuery] = useState("");
  const [dataAnswer, setDataAnswer] = useState("");

  useEffect(() => {
    localStorage.setItem("transactions", JSON.stringify(transactions));
  }, [transactions]);

  function answerDataQuestion() {
    const q = dataQuery.toLowerCase();
    if (q.includes("highest recurring") || q.includes("top recurring") || q.includes("largest recurring")) {
      const top = highestRecurring(transactions, 3);
      setDataAnswer(top ? `Highest recurring expense over last 3 months: ${top.key} — $${top.total.toFixed(2)}` : "No recurring expenses found in the last 3 months.");
    } else {
      setDataAnswer("Try: What was my highest recurring expense in the last 3 months?");
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-blue-50/40">
      <Hero />
      <main id="dashboard" className="container py-8">
        <div className="grid grid-cols-1 gap-6 xl:grid-cols-12">
          <section className="xl:col-span-7 space-y-6">
            <div id="expenses">
              <UploadPanel onData={(txs) => setTransactions(txs)} />
            </div>
            <div id="reports" className="space-y-6">
              <InsightsPanel transactions={transactions} />
              <ChartsPanel transactions={transactions} />
            </div>
            <div id="calculators" className="rounded-2xl border bg-card shadow-sm">
              <div className="p-4 border-b">
                <h2 className="text-lg font-semibold">Ask About Your Data</h2>
                <p className="text-sm text-muted-foreground">Optional personalized query powered by your transactions</p>
              </div>
              <div className="p-4 space-y-3">
                <div className="flex gap-2">
                  <input value={dataQuery} onChange={(e) => setDataQuery(e.target.value)} placeholder="What was my highest recurring expense in the last 3 months?" className="flex-1 rounded-xl border bg-background px-3 py-2 focus:outline-none focus:ring-2 focus:ring-ring" />
                  <button onClick={answerDataQuestion} className="rounded-xl bg-accent px-4 py-2 text-accent-foreground hover:opacity-90">Ask</button>
                </div>
                {dataAnswer && <p className="text-sm">{dataAnswer}</p>}
              </div>
            </div>
            <div id="budget" className="rounded-2xl border bg-card shadow-sm">
              <div className="p-4 border-b">
                <h2 className="text-lg font-semibold">Budget Planner</h2>
                <p className="text-sm text-muted-foreground">Plan with the 50-30-20 rule. Custom categories coming soon.</p>
              </div>
              <div className="p-4 text-sm text-muted-foreground">
                Use insights above to guide your monthly allocations. Sliders and AI-assisted suggestions can be added next.
              </div>
            </div>
          </section>
          <section id="chat" className="xl:col-span-5 h-[720px]">
            <ChatPanel />
          </section>
        </div>
        <section id="faq" className="mt-8 rounded-2xl border bg-card shadow-sm">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold">FAQ / Knowledge Hub</h2>
            <p className="text-sm text-muted-foreground">What is SIP? How to reduce debt? Difference between good and bad debt?</p>
          </div>
          <div className="p-4 text-sm text-muted-foreground">We can expand this section with articles or connect to Builder CMS for content.</div>
        </section>
        <section id="login" className="mt-6 rounded-2xl border bg-card shadow-sm">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold">Login / Signup</h2>
            <p className="text-sm text-muted-foreground">Authenticate to save your data securely.</p>
          </div>
          <div className="p-4 text-sm text-muted-foreground">Hook up auth (e.g., Supabase) to enable personalized dashboards and saved budgets.</div>
        </section>
      </main>
      <FloatingChat />
    </div>
  );
}
