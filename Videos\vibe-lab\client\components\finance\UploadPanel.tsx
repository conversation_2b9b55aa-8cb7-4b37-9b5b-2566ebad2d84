import { useRef, useState } from "react";
import type { Transaction, ClassifyRequest, ClassifyResponse } from "@shared/api";
import { parseCsv } from "@/lib/finance";
import { useAuth } from "@/context/auth";
import { useAuthUI } from "@/context/auth-ui";

export default function UploadPanel({ onData }: { onData: (txs: Transaction[]) => void }) {
  const inputRef = useRef<HTMLInputElement>(null);
  const [parsed, setParsed] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  async function handleFiles(files: FileList | null) {
    setError(null);
    if (!files || files.length === 0) return;
    const file = files[0];
    const text = await file.text();
    let txs: Transaction[] = [];
    try {
      if (file.name.toLowerCase().endsWith(".json")) {
        const data = JSON.parse(text);
        if (Array.isArray(data)) {
          txs = data
            .map((d) => ({ description: String(d.description ?? d.name ?? d.details ?? ""), amount: Number(d.amount), date: d.date ? String(d.date) : undefined, category: d.category ? String(d.category) : undefined }))
            .filter((t) => t.description && !Number.isNaN(t.amount));
        }
      } else {
        txs = parseCsv(text);
      }
    } catch (e: any) {
      setError(e?.message || "Failed to parse file");
      return;
    }
    setParsed(txs);
  }

  async function categorize() {
    if (!parsed.length) return;
    setLoading(true);
    setError(null);
    try {
      const body: ClassifyRequest = { transactions: parsed, labels: ["Food", "Rent", "Transport", "Bills", "Entertainment", "Groceries", "Healthcare", "Shopping", "Travel", "Income", "Other"] };
      const r = await fetch("/api/classify", { method: "POST", headers: { "content-type": "application/json" }, body: JSON.stringify(body) });
      const data = (await r.json()) as ClassifyResponse & { error?: string };
      if ((data as any).error) throw new Error((data as any).error);
      onData(data.categorized);
      localStorage.setItem("transactions", JSON.stringify(data.categorized));
    } catch (e: any) {
      setError(e?.message || "Failed to categorize");
    } finally {
      setLoading(false);
    }
  }

  const { isAuthenticated } = useAuth();
  const ui = useAuthUI();

  return (
    <div className="rounded-2xl border bg-card shadow-sm">
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold">Upload Spending Data</h2>
        <p className="text-sm text-muted-foreground">CSV or JSON with description, amount, and optional date</p>
      </div>
      {!isAuthenticated ? (
        <div className="p-6 text-center">
          <p className="mb-3 text-sm text-muted-foreground">Please register or login to import your files.</p>
          <button onClick={() => ui.openAuth("register")} className="rounded-xl bg-primary px-4 py-2 text-primary-foreground">Register</button>
          <button onClick={() => ui.openAuth("login")} className="ml-2 rounded-xl bg-secondary px-4 py-2">Login</button>
        </div>
      ) : (
        <div className="p-4 space-y-3">
          <div className="flex items-center gap-3">
            <input ref={inputRef} type="file" accept=".csv,.json" onChange={(e) => handleFiles(e.target.files)} className="block w-full text-sm text-foreground" />
            <button onClick={() => inputRef.current?.click()} className="rounded-xl bg-secondary px-4 py-2 text-secondary-foreground hover:opacity-90">Browse</button>
          </div>
          {parsed.length > 0 && (
            <div className="flex items-center justify-between rounded-xl border bg-background p-3">
              <div>
                <p className="font-medium">Parsed {parsed.length} transactions</p>
                <p className="text-sm text-muted-foreground">Click categorize to label each expense automatically</p>
              </div>
              <button onClick={categorize} disabled={loading} className="rounded-xl bg-primary px-4 py-2 text-primary-foreground hover:opacity-90 disabled:opacity-50">{loading ? "Categorizing..." : "Categorize"}</button>
            </div>
          )}
          {error && <p className="text-sm text-destructive">{error}</p>}
        </div>
      )}
    </div>
  );
}
