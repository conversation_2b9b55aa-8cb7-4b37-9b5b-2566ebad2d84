import { useMemo } from "react";
import type { Transaction } from "@shared/api";
import { groupByCategory, groupByMonth } from "@/lib/finance";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>, <PERSON>, <PERSON><PERSON>, ResponsiveContainer, Tooltip, <PERSON>Axis, <PERSON>A<PERSON>s, Cell } from "recharts";

const COLORS = ["#10b981", "#0ea5e9", "#f59e0b", "#ef4444", "#8b5cf6", "#14b8a6", "#f97316", "#22c55e", "#a855f7", "#ec4899", "#84cc16"];

export default function ChartsPanel({ transactions }: { transactions: Transaction[] }) {
  const byCategory = useMemo(() => groupByCategory(transactions), [transactions]);
  const byMonth = useMemo(() => groupByMonth(transactions), [transactions]);

  return (
    <div className="rounded-2xl border bg-card shadow-sm">
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold">Spending Visualizations</h2>
        <p className="text-sm text-muted-foreground">Category breakdown and monthly trend</p>
      </div>
      <div className="p-4 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="h-72">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Tooltip />
              <Legend />
              <Pie dataKey="total" data={byCategory} cx="50%" cy="50%" outerRadius={100} label>
                {byCategory.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="h-72">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={byMonth}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="total" name="Monthly Spend" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
