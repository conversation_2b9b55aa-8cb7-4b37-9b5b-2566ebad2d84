import { createContext, useContext, useMemo, useState } from "react";

type Tab = "login" | "register";

type Ctx = {
  open: boolean;
  tab: Tab;
  openAuth: (tab?: Tab) => void;
  closeAuth: () => void;
  setTab: (tab: Tab) => void;
};

const AuthUIContext = createContext<Ctx | undefined>(undefined);

export function AuthUIProvider({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  const [tab, setTab] = useState<Tab>("register");
  const openAuth = (t?: Tab) => {
    if (t) setTab(t);
    setOpen(true);
  };
  const closeAuth = () => setOpen(false);
  const value = useMemo(() => ({ open, tab, openAuth, closeAuth, setTab }), [open, tab]);
  return <AuthUIContext.Provider value={value}>{children}</AuthUIContext.Provider>;
}

export function useAuthUI() {
  const ctx = useContext(AuthUIContext);
  if (!ctx) throw new Error("useAuthUI must be used within AuthUIProvider");
  return ctx;
}
