import type { Request<PERSON><PERSON><PERSON> } from "express";

const HF_API_BASE = "https://api-inference.huggingface.co/models";

function getToken(): string | undefined {
  return process.env.HUGGING_FACE_API_TOKEN || process.env.HF_API_TOKEN || process.env.HF_TOKEN;
}

async function hfFetch(model: string, payload: unknown, options?: { task?: string }) {
  const token = getToken();
  const res = await fetch(`${HF_API_BASE}/${encodeURIComponent(model)}`, {
    method: "POST",
    headers: {
      "content-type": "application/json",
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
    body: JSON.stringify(payload),
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`Hugging Face API error (${res.status}): ${text}`);
  }
  return res.json();
}

export const chatHandler: RequestHandler = async (req, res) => {
  try {
    const { history, message, model } = req.body as {
      history?: { user: string; assistant: string }[];
      message: string;
      model?: string;
    };
    const m = model || "microsoft/DialoGPT-medium";
    const past_user_inputs = (history || []).map((h) => h.user);
    const generated_responses = (history || []).map((h) => h.assistant);
    const payload = {
      inputs: {
        past_user_inputs,
        generated_responses,
        text: message,
      },
      options: { wait_for_model: true },
    };
    const data = await hfFetch(m, payload);
    const reply: string = Array.isArray(data?.generated_text)
      ? data.generated_text?.[0]
      : data?.generated_text || data?.generated_text?.toString?.() || data?.conversation?.generated_responses?.slice(-1)?.[0] || "";
    res.json({ reply });
  } catch (err: any) {
    res.status(500).json({ error: err.message || "Chat error" });
  }
};

export const classifyExpensesHandler: RequestHandler = async (req, res) => {
  try {
    const { transactions, labels, model } = req.body as {
      transactions: { id?: string; description: string; amount: number; date?: string }[];
      labels?: string[];
      model?: string;
    };
    const candidate_labels = labels && labels.length ? labels : ["Food", "Rent", "Transport", "Bills", "Entertainment", "Groceries", "Healthcare", "Shopping", "Travel", "Income", "Other"];

    const selectedModel = model || process.env.HF_TEXT_CLASSIFICATION_MODEL || "facebook/bart-large-mnli"; // zero-shot

    const categorized = [] as { id?: string; description: string; amount: number; date?: string; category: string }[];

    for (const t of transactions) {
      const payload = {
        inputs: t.description,
        parameters: { candidate_labels, multi_label: false },
        options: { wait_for_model: true },
      };
      const data = await hfFetch(selectedModel, payload);
      const top = Array.isArray(data) ? data[0] : data;
      const labelsResp: string[] = top?.labels || [];
      const scores: number[] = top?.scores || [];
      let category = candidate_labels[0];
      if (labelsResp.length) {
        category = labelsResp[0];
      }
      categorized.push({ ...t, category });
    }

    res.json({ categorized });
  } catch (err: any) {
    res.status(500).json({ error: err.message || "Classification error" });
  }
};

export const summarizeHandler: RequestHandler = async (req, res) => {
  try {
    const { text, model, max_length } = req.body as { text: string; model?: string; max_length?: number };
    const m = model || "facebook/bart-large-cnn";
    const payload = {
      inputs: text,
      parameters: { max_length: max_length || 180, do_sample: false },
      options: { wait_for_model: true },
    };
    const data = await hfFetch(m, payload);
    const summary = Array.isArray(data) ? data[0]?.summary_text || data[0]?.generated_text : data?.summary_text || data?.generated_text;
    res.json({ summary: summary || "" });
  } catch (err: any) {
    res.status(500).json({ error: err.message || "Summarization error" });
  }
};

export const embedHandler: RequestHandler = async (req, res) => {
  try {
    const { texts, model } = req.body as { texts: string[]; model?: string };
    const m = model || "sentence-transformers/all-MiniLM-L6-v2";
    const payload = {
      inputs: texts,
      options: { wait_for_model: true },
    };
    const data = await hfFetch(m, payload);
    res.json({ embeddings: data });
  } catch (err: any) {
    res.status(500).json({ error: err.message || "Embedding error" });
  }
};
