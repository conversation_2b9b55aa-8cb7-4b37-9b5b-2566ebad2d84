import { useState } from "react";
import { useAuth } from "@/context/auth";
import { useAuthUI } from "@/context/auth-ui";

export default function AuthModal() {
  const { open, tab, closeAuth, setTab } = useAuthUI();
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 p-4" role="dialog" aria-modal="true">
      <div className="w-full max-w-md rounded-2xl bg-white shadow-xl">
        <div className="flex items-center justify-between border-b p-4">
          <div className="flex gap-2 text-sm">
            <button className={`rounded-full px-3 py-1 ${tab === "register" ? "bg-primary text-primary-foreground" : "bg-secondary"}`} onClick={() => setTab("register")}>
              Register
            </button>
            <button className={`rounded-full px-3 py-1 ${tab === "login" ? "bg-primary text-primary-foreground" : "bg-secondary"}`} onClick={() => setTab("login")}>
              Login
            </button>
          </div>
          <button onClick={closeAuth} className="rounded-md px-2 py-1 text-sm hover:bg-secondary">✕</button>
        </div>
        <div className="p-4">{tab === "register" ? <RegisterForm /> : <LoginForm />}</div>
      </div>
    </div>
  );
}

function RegisterForm() {
  const { register } = useAuth();
  const { closeAuth } = useAuthUI();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    setError(null);
    try {
      await register({ name, email, password });
      closeAuth();
    } catch (err: any) {
      setError(err?.message || "Registration failed");
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-3">
      <div className="space-y-1">
        <label className="text-sm">Name</label>
        <input value={name} onChange={(e) => setName(e.target.value)} className="w-full rounded-xl border bg-background px-3 py-2" required />
      </div>
      <div className="space-y-1">
        <label className="text-sm">Email</label>
        <input type="email" value={email} onChange={(e) => setEmail(e.target.value)} className="w-full rounded-xl border bg-background px-3 py-2" required />
      </div>
      <div className="space-y-1">
        <label className="text-sm">Password</label>
        <input type="password" value={password} onChange={(e) => setPassword(e.target.value)} className="w-full rounded-xl border bg-background px-3 py-2" required />
      </div>
      {error && <p className="text-sm text-destructive">{error}</p>}
      <button type="submit" className="w-full rounded-xl bg-primary px-4 py-2 text-primary-foreground">Create account</button>
    </form>
  );
}

function LoginForm() {
  const { login } = useAuth();
  const { closeAuth } = useAuthUI();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    setError(null);
    try {
      await login({ email, password });
      closeAuth();
    } catch (err: any) {
      setError(err?.message || "Login failed");
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-3">
      <div className="space-y-1">
        <label className="text-sm">Email</label>
        <input type="email" value={email} onChange={(e) => setEmail(e.target.value)} className="w-full rounded-xl border bg-background px-3 py-2" required />
      </div>
      <div className="space-y-1">
        <label className="text-sm">Password</label>
        <input type="password" value={password} onChange={(e) => setPassword(e.target.value)} className="w-full rounded-xl border bg-background px-3 py-2" required />
      </div>
      {error && <p className="text-sm text-destructive">{error}</p>}
      <button type="submit" className="w-full rounded-xl bg-primary px-4 py-2 text-primary-foreground">Login</button>
    </form>
  );
}
