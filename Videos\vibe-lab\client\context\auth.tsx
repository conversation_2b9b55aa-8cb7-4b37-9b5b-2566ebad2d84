import { createContext, use<PERSON>allback, useContext, useEffect, useMemo, useState } from "react";

type User = { id: string; name: string; email: string };

type AuthContextType = {
  user: User | null;
  isAuthenticated: boolean;
  register: (input: { name: string; email: string; password: string }) => Promise<void>;
  login: (input: { email: string; password: string }) => Promise<void>;
  logout: () => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

function hash(s: string) {
  let h = 0;
  for (let i = 0; i < s.length; i++) h = (h << 5) - h + s.charCodeAt(i);
  return String(h >>> 0);
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    try {
      const raw = localStorage.getItem("auth_user");
      setUser(raw ? (JSON.parse(raw) as User) : null);
    } catch {
      setUser(null);
    }
  }, []);

  const register = useCallback(async ({ name, email, password }: { name: string; email: string; password: string }) => {
    const users = JSON.parse(localStorage.getItem("users") || "{}") as Record<string, { name: string; email: string; passwordHash: string }>;
    if (users[email]) throw new Error("Email already registered");
    users[email] = { name, email, passwordHash: hash(password) };
    localStorage.setItem("users", JSON.stringify(users));
    const u: User = { id: hash(email + Date.now()), name, email };
    localStorage.setItem("auth_user", JSON.stringify(u));
    setUser(u);
  }, []);

  const login = useCallback(async ({ email, password }: { email: string; password: string }) => {
    const users = JSON.parse(localStorage.getItem("users") || "{}") as Record<string, { name: string; email: string; passwordHash: string }>;
    const rec = users[email];
    if (!rec || rec.passwordHash !== hash(password)) throw new Error("Invalid credentials");
    const u: User = { id: hash(email), name: rec.name, email: rec.email };
    localStorage.setItem("auth_user", JSON.stringify(u));
    setUser(u);
  }, []);

  const logout = useCallback(() => {
    localStorage.removeItem("auth_user");
    setUser(null);
  }, []);

  const value = useMemo(() => ({ user, isAuthenticated: !!user, register, login, logout }), [user, register, login, logout]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error("useAuth must be used within AuthProvider");
  return ctx;
}
