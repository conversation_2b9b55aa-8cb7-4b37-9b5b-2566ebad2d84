/**
 * Shared code between client and server
 * Useful to share types between client and server
 * and/or small pure JS functions that can be used on both client and server
 */

/**
 * Example response type for /api/demo
 */
export interface DemoResponse {
  message: string;
}

export type Transaction = {
  id?: string;
  date?: string; // ISO date string
  description: string;
  amount: number; // positive expense, negative for refunds/income
  category?: string;
};

export interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

export interface ChatRequest {
  history: { user: string; assistant: string }[];
  message: string;
  model?: string;
}

export interface ChatResponse {
  reply: string;
}

export interface ClassifyRequest {
  transactions: Transaction[];
  labels?: string[];
  model?: string;
}

export interface ClassifyResponse {
  categorized: Required<Transaction>[];
}

export interface SummarizeRequest {
  text: string;
  model?: string;
  max_length?: number;
}

export interface SummarizeResponse {
  summary: string;
}

export interface EmbedRequest {
  texts: string[];
  model?: string;
}

export interface EmbedResponse {
  embeddings: number[][];
}
