import { useAuth } from "@/context/auth";
import { useAuthUI } from "@/context/auth-ui";

export default function Hero() {
  return (
    <section id="home" className="bg-white">
      <div className="container grid gap-10 py-12 md:grid-cols-2 md:py-16">
        <div className="flex flex-col justify-center">
          <p className="text-primary font-semibold">FinWise AI</p>
          <h1 className="mt-3 text-4xl font-extrabold leading-tight tracking-tight text-foreground md:text-5xl">
            Guiding you through life's financial journey
          </h1>
          <p className="mt-4 text-muted-foreground max-w-prose">
            AI-powered financial advisor that helps you budget, track expenses, run calculators, and chat for guidance.
          </p>
          <div className="mt-6 flex items-center gap-3">
            <input
              type="email"
              placeholder="Enter your email"
              className="w-full max-w-xs rounded-xl border bg-background px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-ring"
            />
            <HeroCTA />
          </div>
          <div className="mt-6 flex flex-wrap items-center gap-2 text-sm">
            <span className="rounded-full bg-secondary px-3 py-1">Budgeting</span>
            <span className="rounded-full bg-secondary px-3 py-1">Expense Tracking</span>
            <span className="rounded-full bg-secondary px-3 py-1">Calculators</span>
            <span className="rounded-full bg-secondary px-3 py-1">AI Chatbot</span>
          </div>
        </div>
        <div className="relative">
          <div className="absolute -top-6 -left-6 h-40 w-40 rounded-full bg-primary/10 blur-2xl" />
          <img
            alt="Finance advisor"
            className="relative z-10 mx-auto w-[85%] max-w-md rounded-3xl object-cover shadow-xl"
            src="https://images.unsplash.com/photo-1553729784-e91953dec042?q=80&w=1200&auto=format&fit=crop"
          />
        </div>
      </div>
    </section>
  );
}

function HeroCTA() {
  const { isAuthenticated } = useAuth();
  const ui = useAuthUI();
  function handleClick(e: React.MouseEvent) {
    e.preventDefault();
    if (!isAuthenticated) ui.openAuth("register");
    else window.location.hash = "#chat";
  }
  return (
    <a href="#chat" onClick={handleClick} className="rounded-xl bg-primary px-5 py-3 text-primary-foreground font-medium hover:opacity-90">
      Chat with FinWise AI
    </a>
  );
}
